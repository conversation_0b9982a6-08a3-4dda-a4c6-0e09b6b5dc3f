import { create } from "zustand";
import type { AlertSend } from "../types";

export const useBearStore = create<AlertSend[]>((set, get) => ({
  selectedRows: [],
  setSelectedRows: (selectedRows: AlertSend[]) => set({ selectedRows }),
  addSelectedRow: (row: AlertSend) => set((state) => ({ selectedRows: [...state.selectedRows, row] })),
  removeSelectedRow: (row: AlertSend) => set((state) => ({ selectedRows: state.selectedRows.filter((r) => r.id !== row.id) })),
}));
